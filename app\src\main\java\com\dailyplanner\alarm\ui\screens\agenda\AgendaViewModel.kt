package com.dailyplanner.alarm.ui.screens.agenda

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.dailyplanner.alarm.data.dao.AgendaDao
import com.dailyplanner.alarm.data.entities.AgendaItem
import com.dailyplanner.alarm.data.entities.Priority
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.todayIn
import javax.inject.Inject

@HiltViewModel
class AgendaViewModel @Inject constructor(
    private val agendaDao: AgendaDao
) : ViewModel() {

    private val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
    
    private val _uiState = MutableStateFlow(AgendaUiState())
    val uiState: StateFlow<AgendaUiState> = _uiState.asStateFlow()

    init {
        loadTodayAgenda()
    }

    private fun loadTodayAgenda() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            
            agendaDao.getAgendaForDate(today)
                .catch { error ->
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            errorMessage = "Failed to load agenda: ${error.message}"
                        )
                    }
                }
                .collect { items ->
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            agendaItems = items,
                            errorMessage = null
                        )
                    }
                }
        }
    }

    fun showAddItemDialog() {
        _uiState.update { 
            it.copy(
                showAddEditDialog = true,
                editingItem = null
            )
        }
    }

    fun editItem(item: AgendaItem) {
        _uiState.update { 
            it.copy(
                showAddEditDialog = true,
                editingItem = item
            )
        }
    }

    fun hideAddEditDialog() {
        _uiState.update { 
            it.copy(
                showAddEditDialog = false,
                editingItem = null
            )
        }
    }

    fun addItem(item: AgendaItem) {
        viewModelScope.launch {
            try {
                agendaDao.insertAgendaItem(item.copy(date = today))
                hideAddEditDialog()
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(errorMessage = "Failed to add item: ${e.message}")
                }
            }
        }
    }

    fun updateItem(item: AgendaItem) {
        viewModelScope.launch {
            try {
                agendaDao.updateAgendaItem(
                    item.copy(updatedAt = System.currentTimeMillis())
                )
                hideAddEditDialog()
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(errorMessage = "Failed to update item: ${e.message}")
                }
            }
        }
    }

    fun deleteItem(itemId: Long) {
        viewModelScope.launch {
            try {
                agendaDao.deleteAgendaItemById(itemId)
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(errorMessage = "Failed to delete item: ${e.message}")
                }
            }
        }
    }

    fun toggleItemComplete(itemId: Long) {
        viewModelScope.launch {
            try {
                val item = agendaDao.getAgendaItemById(itemId)
                item?.let {
                    val newCompletedState = !it.isCompleted
                    val completedAt = if (newCompletedState) System.currentTimeMillis() else null
                    agendaDao.setItemCompleted(itemId, newCompletedState, completedAt)
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(errorMessage = "Failed to update item: ${e.message}")
                }
            }
        }
    }

    fun clearError() {
        _uiState.update { it.copy(errorMessage = null) }
    }
}

data class AgendaUiState(
    val isLoading: Boolean = false,
    val agendaItems: List<AgendaItem> = emptyList(),
    val showAddEditDialog: Boolean = false,
    val editingItem: AgendaItem? = null,
    val errorMessage: String? = null
)
