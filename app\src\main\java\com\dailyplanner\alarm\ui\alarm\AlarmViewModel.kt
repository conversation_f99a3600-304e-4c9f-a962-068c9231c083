package com.dailyplanner.alarm.ui.alarm

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.dailyplanner.alarm.data.dao.AgendaDao
import com.dailyplanner.alarm.data.dao.UserInteractionDao
import com.dailyplanner.alarm.data.entities.AgendaItem
import com.dailyplanner.alarm.data.entities.UserInteraction
import com.dailyplanner.alarm.data.entities.InputType
import com.dailyplanner.alarm.llm.LLMService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.todayIn
import java.util.UUID
import javax.inject.Inject

@HiltViewModel
class AlarmViewModel @Inject constructor(
    private val agendaDao: AgendaDao,
    private val userInteractionDao: UserInteractionDao,
    private val llmService: LLMService
) : ViewModel() {

    private val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
    private val sessionId = UUID.randomUUID().toString()
    
    private val _uiState = MutableStateFlow(AlarmUiState())
    val uiState: StateFlow<AlarmUiState> = _uiState.asStateFlow()

    fun loadTodayAgenda() {
        viewModelScope.launch {
            agendaDao.getIncompleteAgendaForDate(today)
                .catch { error ->
                    _uiState.update { 
                        it.copy(errorMessage = "Failed to load agenda: ${error.message}")
                    }
                }
                .collect { items ->
                    _uiState.update { 
                        it.copy(todayAgenda = items)
                    }
                }
        }
    }

    fun generateWakeUpMessage() {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isGeneratingMessage = true) }
                
                val currentAgenda = _uiState.value.todayAgenda
                val message = llmService.processWakeUpInteraction(
                    agendaItems = currentAgenda,
                    inputType = InputType.WAKE_UP_INTERACTION
                )
                
                // Save the interaction
                val interaction = UserInteraction(
                    userInput = "Wake up alarm triggered",
                    inputType = InputType.WAKE_UP_INTERACTION,
                    llmResponse = message,
                    sessionId = sessionId,
                    context = "Today's agenda: ${currentAgenda.size} items"
                )
                userInteractionDao.insertInteraction(interaction)
                
                _uiState.update { 
                    it.copy(
                        wakeUpMessage = message,
                        isGeneratingMessage = false
                    )
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        wakeUpMessage = "Good morning! Ready to start your day? You have ${_uiState.value.todayAgenda.size} items on your agenda today.",
                        isGeneratingMessage = false,
                        errorMessage = "AI assistant unavailable: ${e.message}"
                    )
                }
            }
        }
    }

    fun processUserInput(input: String, inputType: InputType = InputType.TEXT) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isProcessingInput = true) }
                
                val currentAgenda = _uiState.value.todayAgenda
                val context = buildString {
                    append("Current time: ${Clock.System.now()}\n")
                    append("Today's incomplete agenda items:\n")
                    currentAgenda.forEach { item ->
                        append("- ${item.title}")
                        item.time?.let { time -> append(" at $time") }
                        append(" (${item.priority.displayName} priority)\n")
                    }
                }
                
                val response = llmService.processGeneralInteraction(
                    userInput = input,
                    context = context
                )
                
                // Save the interaction
                val interaction = UserInteraction(
                    userInput = input,
                    inputType = inputType,
                    llmResponse = response,
                    sessionId = sessionId,
                    context = context
                )
                userInteractionDao.insertInteraction(interaction)
                
                _uiState.update { 
                    it.copy(
                        lastResponse = response,
                        isProcessingInput = false
                    )
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        lastResponse = "I'm having trouble processing that right now. Please try again.",
                        isProcessingInput = false,
                        errorMessage = "Failed to process input: ${e.message}"
                    )
                }
            }
        }
    }

    fun clearError() {
        _uiState.update { it.copy(errorMessage = null) }
    }
}

data class AlarmUiState(
    val todayAgenda: List<AgendaItem> = emptyList(),
    val wakeUpMessage: String = "",
    val lastResponse: String = "",
    val isGeneratingMessage: Boolean = false,
    val isProcessingInput: Boolean = false,
    val errorMessage: String? = null
)
