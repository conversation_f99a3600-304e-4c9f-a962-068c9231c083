package com.dailyplanner.alarm.llm

import com.dailyplanner.alarm.data.entities.AgendaItem
import com.dailyplanner.alarm.data.entities.UserInteraction
import com.dailyplanner.alarm.data.entities.InputType
import com.dailyplanner.alarm.network.OpenAIApi
import com.dailyplanner.alarm.network.ChatRequest
import com.dailyplanner.alarm.network.ChatMessage
import kotlinx.datetime.Clock
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.todayIn
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class LLMService @Inject constructor(
    private val openAIApi: OpenAIApi
) {
    
    suspend fun processWakeUpInteraction(
        agendaItems: List<AgendaItem>,
        userInput: String = "",
        inputType: InputType = InputType.WAKE_UP_INTERACTION
    ): String {
        val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
        val context = buildWakeUpContext(agendaItems, today)
        
        val systemPrompt = """
            You are a helpful AI assistant integrated into a daily planner alarm app. 
            The user has just woken up and you should:
            1. Greet them warmly
            2. Present their agenda for today in a clear, motivating way
            3. Offer to help them plan or adjust their day
            4. Be encouraging and positive
            5. Keep responses concise but friendly
            
            Current date: $today
            
            User's agenda for today:
            $context
        """.trimIndent()
        
        val messages = mutableListOf(
            ChatMessage("system", systemPrompt)
        )
        
        if (userInput.isNotEmpty()) {
            messages.add(ChatMessage("user", userInput))
        } else {
            messages.add(ChatMessage("user", "Good morning! What's on my agenda today?"))
        }
        
        return try {
            val request = ChatRequest(
                model = "gpt-3.5-turbo",
                messages = messages,
                maxTokens = 300,
                temperature = 0.7
            )
            
            val response = openAIApi.createChatCompletion(request)
            response.choices.firstOrNull()?.message?.content 
                ?: "Good morning! I'm here to help you start your day. Let me know how I can assist you!"
        } catch (e: Exception) {
            "Good morning! I'm having trouble connecting right now, but I'm here to help you plan your day. What would you like to focus on today?"
        }
    }
    
    suspend fun processGeneralInteraction(
        userInput: String,
        context: String? = null,
        conversationHistory: List<UserInteraction> = emptyList()
    ): String {
        val systemPrompt = """
            You are a helpful AI assistant for a daily planner app. You can help users:
            1. Plan and organize their daily tasks
            2. Set priorities and manage time
            3. Provide motivation and productivity tips
            4. Answer questions about their schedule
            5. Help them add, modify, or understand their agenda items
            
            Be helpful, concise, and encouraging. Focus on productivity and organization.
            
            ${context?.let { "Current context: $it" } ?: ""}
        """.trimIndent()
        
        val messages = mutableListOf(
            ChatMessage("system", systemPrompt)
        )
        
        // Add recent conversation history for context
        conversationHistory.takeLast(6).forEach { interaction ->
            messages.add(ChatMessage("user", interaction.userInput))
            messages.add(ChatMessage("assistant", interaction.llmResponse))
        }
        
        messages.add(ChatMessage("user", userInput))
        
        return try {
            val request = ChatRequest(
                model = "gpt-3.5-turbo",
                messages = messages,
                maxTokens = 250,
                temperature = 0.7
            )
            
            val response = openAIApi.createChatCompletion(request)
            response.choices.firstOrNull()?.message?.content 
                ?: "I'm here to help you with your daily planning. Could you please rephrase your question?"
        } catch (e: Exception) {
            "I'm having trouble connecting right now. Please try again in a moment, or let me know if there's something specific I can help you with regarding your daily planning."
        }
    }
    
    private fun buildWakeUpContext(agendaItems: List<AgendaItem>, date: LocalDate): String {
        if (agendaItems.isEmpty()) {
            return "You have no scheduled items for today. It's a great day to plan something meaningful!"
        }
        
        val incompleteItems = agendaItems.filter { !it.isCompleted }
        val priorityGroups = incompleteItems.groupBy { it.priority }
        
        val context = StringBuilder()
        
        priorityGroups.forEach { (priority, items) ->
            context.append("${priority.displayName} Priority:\n")
            items.forEach { item ->
                val timeStr = item.time?.let { " at ${it}" } ?: ""
                val durationStr = item.estimatedDurationMinutes?.let { " (${it}min)" } ?: ""
                context.append("- ${item.title}$timeStr$durationStr\n")
                if (item.description.isNotEmpty()) {
                    context.append("  ${item.description}\n")
                }
            }
            context.append("\n")
        }
        
        return context.toString().trim()
    }
}
