package com.dailyplanner.alarm.alarm

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import com.dailyplanner.alarm.data.dao.AlarmDao
import javax.inject.Inject

@AndroidEntryPoint
class BootReceiver : BroadcastReceiver() {

    @Inject
    lateinit var alarmDao: AlarmDao
    
    @Inject
    lateinit var alarmScheduler: AlarmScheduler

    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == Intent.ACTION_BOOT_COMPLETED ||
            intent.action == Intent.ACTION_MY_PACKAGE_REPLACED ||
            intent.action == Intent.ACTION_PACKAGE_REPLACED) {
            
            Log.d("BootReceiver", "Device booted or app updated, rescheduling alarms")
            
            // Reschedule all enabled alarms
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    alarmDao.getEnabledAlarms().collect { alarms ->
                        alarms.forEach { alarm ->
                            alarmScheduler.scheduleAlarm(alarm)
                            Log.d("BootReceiver", "Rescheduled alarm: ${alarm.id}")
                        }
                    }
                } catch (e: Exception) {
                    Log.e("BootReceiver", "Error rescheduling alarms", e)
                }
            }
        }
    }
}
