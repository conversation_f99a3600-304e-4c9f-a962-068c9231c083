package com.dailyplanner.alarm.data.dao

import androidx.room.*
import com.dailyplanner.alarm.data.entities.AgendaItem
import com.dailyplanner.alarm.data.entities.Priority
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDate

@Dao
interface AgendaDao {
    
    @Query("SELECT * FROM agenda_items WHERE date = :date ORDER BY priority DESC, time ASC")
    fun getAgendaForDate(date: LocalDate): Flow<List<AgendaItem>>
    
    @Query("SELECT * FROM agenda_items WHERE date = :date AND isCompleted = 0 ORDER BY priority DESC, time ASC")
    fun getIncompleteAgendaForDate(date: LocalDate): Flow<List<AgendaItem>>
    
    @Query("SELECT * FROM agenda_items WHERE date >= :startDate AND date <= :endDate ORDER BY date ASC, priority DESC, time ASC")
    fun getAgendaForDateRange(startDate: LocalDate, endDate: LocalDate): Flow<List<AgendaItem>>
    
    @Query("SELECT * FROM agenda_items WHERE id = :id")
    suspend fun getAgendaItemById(id: Long): AgendaItem?
    
    @Query("SELECT * FROM agenda_items WHERE isCompleted = 0 AND date <= :date ORDER BY priority DESC, date ASC, time ASC")
    fun getOverdueItems(date: LocalDate): Flow<List<AgendaItem>>
    
    @Insert
    suspend fun insertAgendaItem(item: AgendaItem): Long
    
    @Update
    suspend fun updateAgendaItem(item: AgendaItem)
    
    @Delete
    suspend fun deleteAgendaItem(item: AgendaItem)
    
    @Query("DELETE FROM agenda_items WHERE id = :id")
    suspend fun deleteAgendaItemById(id: Long)
    
    @Query("UPDATE agenda_items SET isCompleted = :completed, completedAt = :completedAt WHERE id = :id")
    suspend fun setItemCompleted(id: Long, completed: Boolean, completedAt: Long?)
    
    @Query("SELECT COUNT(*) FROM agenda_items WHERE date = :date AND isCompleted = 0")
    suspend fun getIncompleteItemsCount(date: LocalDate): Int
}
