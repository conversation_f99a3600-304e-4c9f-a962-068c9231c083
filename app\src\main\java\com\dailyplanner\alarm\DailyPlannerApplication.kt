package com.dailyplanner.alarm

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.os.Build
import dagger.hilt.android.HiltAndroidApp

@HiltAndroidApp
class DailyPlannerApplication : Application() {

    companion object {
        const val ALARM_CHANNEL_ID = "alarm_channel"
        const val SPEECH_CHANNEL_ID = "speech_channel"
        const val GENERAL_CHANNEL_ID = "general_channel"
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannels()
    }

    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(NotificationManager::class.java)

            // Alarm Channel
            val alarmChannel = NotificationChannel(
                ALARM_CHANNEL_ID,
                "Alarms",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for alarms and wake-up reminders"
                enableVibration(true)
                setBypassDnd(true)
            }

            // Speech Channel
            val speechChannel = NotificationChannel(
                SPEECH_CHANNEL_ID,
                "Speech Processing",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Background speech processing and LLM interactions"
            }

            // General Channel
            val generalChannel = NotificationChannel(
                GENERAL_CHANNEL_ID,
                "General",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "General app notifications"
            }

            notificationManager.createNotificationChannels(
                listOf(alarmChannel, speechChannel, generalChannel)
            )
        }
    }
}
