package com.dailyplanner.alarm.ui.screens.agenda

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.dailyplanner.alarm.data.entities.AgendaItem
import com.dailyplanner.alarm.data.entities.Priority
import kotlinx.datetime.LocalTime

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddEditItemDialog(
    item: AgendaItem?,
    onDismiss: () -> Unit,
    onSave: (AgendaItem) -> Unit
) {
    var title by remember { mutableStateOf(item?.title ?: "") }
    var description by remember { mutableStateOf(item?.description ?: "") }
    var category by remember { mutableStateOf(item?.category ?: "") }
    var priority by remember { mutableStateOf(item?.priority ?: Priority.MEDIUM) }
    var hasTime by remember { mutableStateOf(item?.time != null) }
    var timeHour by remember { mutableStateOf(item?.time?.hour ?: 9) }
    var timeMinute by remember { mutableStateOf(item?.time?.minute ?: 0) }
    var hasDuration by remember { mutableStateOf(item?.estimatedDurationMinutes != null) }
    var duration by remember { mutableStateOf(item?.estimatedDurationMinutes?.toString() ?: "30") }
    var hasReminder by remember { mutableStateOf(item?.reminderMinutesBefore != null) }
    var reminder by remember { mutableStateOf(item?.reminderMinutesBefore?.toString() ?: "15") }

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = MaterialTheme.shapes.large
        ) {
            Column(
                modifier = Modifier
                    .padding(24.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                Text(
                    text = if (item == null) "Add Item" else "Edit Item",
                    style = MaterialTheme.typography.headlineSmall
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Title
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    label = { Text("Title") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // Description
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("Description (optional)") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // Category
                OutlinedTextField(
                    value = category,
                    onValueChange = { category = it },
                    label = { Text("Category (optional)") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Priority
                Text(
                    text = "Priority",
                    style = MaterialTheme.typography.titleMedium
                )
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Priority.values().forEach { priorityOption ->
                        FilterChip(
                            onClick = { priority = priorityOption },
                            label = { Text(priorityOption.displayName) },
                            selected = priority == priorityOption,
                            modifier = Modifier.weight(1f)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Time
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = hasTime,
                        onCheckedChange = { hasTime = it }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Set specific time")
                }
                
                if (hasTime) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        OutlinedTextField(
                            value = timeHour.toString(),
                            onValueChange = { 
                                it.toIntOrNull()?.let { hour ->
                                    if (hour in 0..23) timeHour = hour
                                }
                            },
                            label = { Text("Hour") },
                            modifier = Modifier.weight(1f),
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                            singleLine = true
                        )
                        Text(":")
                        OutlinedTextField(
                            value = timeMinute.toString(),
                            onValueChange = { 
                                it.toIntOrNull()?.let { minute ->
                                    if (minute in 0..59) timeMinute = minute
                                }
                            },
                            label = { Text("Minute") },
                            modifier = Modifier.weight(1f),
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                            singleLine = true
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Duration
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = hasDuration,
                        onCheckedChange = { hasDuration = it }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Estimated duration")
                }
                
                if (hasDuration) {
                    Spacer(modifier = Modifier.height(8.dp))
                    OutlinedTextField(
                        value = duration,
                        onValueChange = { duration = it },
                        label = { Text("Duration (minutes)") },
                        modifier = Modifier.fillMaxWidth(),
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        singleLine = true
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Reminder
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = hasReminder,
                        onCheckedChange = { hasReminder = it }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Reminder")
                }
                
                if (hasReminder) {
                    Spacer(modifier = Modifier.height(8.dp))
                    OutlinedTextField(
                        value = reminder,
                        onValueChange = { reminder = it },
                        label = { Text("Minutes before") },
                        modifier = Modifier.fillMaxWidth(),
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        singleLine = true
                    )
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // Buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("Cancel")
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                    Button(
                        onClick = {
                            if (title.isNotBlank()) {
                                val newItem = AgendaItem(
                                    id = item?.id ?: 0,
                                    title = title.trim(),
                                    description = description.trim(),
                                    date = item?.date ?: kotlinx.datetime.Clock.System.todayIn(kotlinx.datetime.TimeZone.currentSystemDefault()),
                                    time = if (hasTime) LocalTime(timeHour, timeMinute) else null,
                                    priority = priority,
                                    category = category.trim(),
                                    estimatedDurationMinutes = if (hasDuration) duration.toIntOrNull() else null,
                                    reminderMinutesBefore = if (hasReminder) reminder.toIntOrNull() else null,
                                    isCompleted = item?.isCompleted ?: false,
                                    createdAt = item?.createdAt ?: System.currentTimeMillis(),
                                    completedAt = item?.completedAt
                                )
                                onSave(newItem)
                            }
                        },
                        enabled = title.isNotBlank()
                    ) {
                        Text("Save")
                    }
                }
            }
        }
    }
}
