package com.dailyplanner.alarm.ui.navigation

import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import com.dailyplanner.alarm.ui.screens.agenda.AgendaScreen
import com.dailyplanner.alarm.ui.screens.alarms.AlarmsScreen
import com.dailyplanner.alarm.ui.screens.chat.ChatScreen

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DailyPlannerNavigation(navController: NavHostController) {
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route

    Scaffold(
        bottomBar = {
            NavigationBar {
                bottomNavItems.forEach { item ->
                    NavigationBarItem(
                        icon = { Icon(item.icon, contentDescription = item.title) },
                        label = { Text(item.title) },
                        selected = currentRoute == item.route,
                        onClick = {
                            if (currentRoute != item.route) {
                                navController.navigate(item.route) {
                                    popUpTo(navController.graph.startDestinationId) {
                                        saveState = true
                                    }
                                    launchSingleTop = true
                                    restoreState = true
                                }
                            }
                        }
                    )
                }
            }
        }
    ) { paddingValues ->
        NavHost(
            navController = navController,
            startDestination = Screen.Agenda.route,
            modifier = Modifier.padding(paddingValues)
        ) {
            composable(Screen.Agenda.route) {
                AgendaScreen(navController = navController)
            }
            composable(Screen.Alarms.route) {
                AlarmsScreen(navController = navController)
            }
            composable(Screen.Chat.route) {
                ChatScreen(navController = navController)
            }
        }
    }
}

sealed class Screen(val route: String, val title: String, val icon: androidx.compose.ui.graphics.vector.ImageVector) {
    object Agenda : Screen("agenda", "Agenda", Icons.Default.CalendarToday)
    object Alarms : Screen("alarms", "Alarms", Icons.Default.Alarm)
    object Chat : Screen("chat", "Assistant", Icons.Default.Chat)
}

val bottomNavItems = listOf(
    Screen.Agenda,
    Screen.Alarms,
    Screen.Chat
)
