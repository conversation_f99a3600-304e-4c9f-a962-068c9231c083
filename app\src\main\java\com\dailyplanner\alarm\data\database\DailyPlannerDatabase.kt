package com.dailyplanner.alarm.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.dailyplanner.alarm.data.converters.Converters
import com.dailyplanner.alarm.data.dao.AlarmDao
import com.dailyplanner.alarm.data.dao.AgendaDao
import com.dailyplanner.alarm.data.dao.UserInteractionDao
import com.dailyplanner.alarm.data.entities.Alarm
import com.dailyplanner.alarm.data.entities.AgendaItem
import com.dailyplanner.alarm.data.entities.UserInteraction

@Database(
    entities = [
        Alarm::class,
        AgendaItem::class,
        UserInteraction::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class DailyPlannerDatabase : RoomDatabase() {
    
    abstract fun alarmDao(): AlarmDao
    abstract fun agendaDao(): AgendaDao
    abstract fun userInteractionDao(): UserInteractionDao
    
    companion object {
        const val DATABASE_NAME = "daily_planner_database"
        
        @Volatile
        private var INSTANCE: DailyPlannerDatabase? = null
        
        fun getDatabase(context: Context): DailyPlannerDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    DailyPlannerDatabase::class.java,
                    DATABASE_NAME
                )
                .fallbackToDestructiveMigration()
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
