package com.dailyplanner.alarm.speech

import android.app.Notification
import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.speech.tts.TextToSpeech
import android.speech.tts.UtteranceProgressListener
import android.util.Log
import androidx.core.app.NotificationCompat
import com.dailyplanner.alarm.DailyPlannerApplication
import com.dailyplanner.alarm.R
import kotlinx.coroutines.*
import java.util.*
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

class SpeechService : Service(), TextToSpeech.OnInitListener {

    private var textToSpeech: TextToSpeech? = null
    private var isInitialized = false
    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private val pendingSpeechQueue = mutableListOf<String>()

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onCreate() {
        super.onCreate()
        textToSpeech = TextToSpeech(this, this)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val text = intent?.getStringExtra(EXTRA_TEXT_TO_SPEAK)
        val shouldStartForeground = intent?.getBooleanExtra(EXTRA_START_FOREGROUND, false) ?: false

        if (shouldStartForeground) {
            startForeground(NOTIFICATION_ID, createNotification())
        }

        text?.let { speakText(it) }

        return START_NOT_STICKY
    }

    override fun onInit(status: Int) {
        if (status == TextToSpeech.SUCCESS) {
            textToSpeech?.let { tts ->
                val result = tts.setLanguage(Locale.getDefault())
                if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                    Log.e("SpeechService", "Language not supported")
                    tts.setLanguage(Locale.US) // Fallback to US English
                }
                
                tts.setSpeechRate(0.9f)
                tts.setPitch(1.0f)
                
                isInitialized = true
                
                // Process any pending speech requests
                processPendingSpeech()
            }
        } else {
            Log.e("SpeechService", "TextToSpeech initialization failed")
        }
    }

    private fun speakText(text: String) {
        if (isInitialized) {
            textToSpeech?.let { tts ->
                val utteranceId = "utterance_${System.currentTimeMillis()}"
                
                tts.setOnUtteranceProgressListener(object : UtteranceProgressListener() {
                    override fun onStart(utteranceId: String?) {
                        Log.d("SpeechService", "Started speaking: $utteranceId")
                    }

                    override fun onDone(utteranceId: String?) {
                        Log.d("SpeechService", "Finished speaking: $utteranceId")
                        serviceScope.launch {
                            delay(1000) // Small delay before stopping service
                            if (pendingSpeechQueue.isEmpty()) {
                                stopSelf()
                            }
                        }
                    }

                    override fun onError(utteranceId: String?) {
                        Log.e("SpeechService", "Error speaking: $utteranceId")
                        stopSelf()
                    }
                })

                val result = tts.speak(text, TextToSpeech.QUEUE_ADD, null, utteranceId)
                if (result == TextToSpeech.ERROR) {
                    Log.e("SpeechService", "Error queuing text for speech")
                    stopSelf()
                }
            }
        } else {
            // Queue the text to be spoken once TTS is initialized
            pendingSpeechQueue.add(text)
        }
    }

    private fun processPendingSpeech() {
        pendingSpeechQueue.forEach { text ->
            speakText(text)
        }
        pendingSpeechQueue.clear()
    }

    suspend fun speakTextAsync(text: String): Boolean = suspendCoroutine { continuation ->
        if (isInitialized) {
            textToSpeech?.let { tts ->
                val utteranceId = "async_utterance_${System.currentTimeMillis()}"
                
                tts.setOnUtteranceProgressListener(object : UtteranceProgressListener() {
                    override fun onStart(utteranceId: String?) {}

                    override fun onDone(utteranceId: String?) {
                        continuation.resume(true)
                    }

                    override fun onError(utteranceId: String?) {
                        continuation.resume(false)
                    }
                })

                val result = tts.speak(text, TextToSpeech.QUEUE_ADD, null, utteranceId)
                if (result == TextToSpeech.ERROR) {
                    continuation.resume(false)
                }
            } ?: continuation.resume(false)
        } else {
            continuation.resume(false)
        }
    }

    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, DailyPlannerApplication.SPEECH_CHANNEL_ID)
            .setContentTitle("Daily Planner")
            .setContentText("Processing speech...")
            .setSmallIcon(R.drawable.ic_mic)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }

    override fun onDestroy() {
        super.onDestroy()
        textToSpeech?.apply {
            stop()
            shutdown()
        }
        serviceScope.cancel()
    }

    companion object {
        private const val NOTIFICATION_ID = 1002
        const val EXTRA_TEXT_TO_SPEAK = "text_to_speak"
        const val EXTRA_START_FOREGROUND = "start_foreground"
    }
}
