package com.dailyplanner.alarm.alarm

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import com.dailyplanner.alarm.data.entities.Alarm
import kotlinx.datetime.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AlarmScheduler @Inject constructor(
    private val context: Context
) {
    private val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager

    fun scheduleAlarm(alarm: Alarm) {
        if (!alarm.isEnabled) return

        val intent = Intent(context, AlarmReceiver::class.java).apply {
            putExtra(EXTRA_ALARM_ID, alarm.id)
            putExtra(EXTRA_ALARM_LABEL, alarm.label)
        }

        val pendingIntent = PendingIntent.getBroadcast(
            context,
            alarm.id.toInt(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val triggerTime = calculateNextAlarmTime(alarm)

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    triggerTime,
                    pendingIntent
                )
            } else {
                alarmManager.setExact(
                    AlarmManager.RTC_WAKEUP,
                    triggerTime,
                    pendingIntent
                )
            }
        } catch (e: SecurityException) {
            // Handle case where exact alarm permission is not granted
            alarmManager.setAndAllowWhileIdle(
                AlarmManager.RTC_WAKEUP,
                triggerTime,
                pendingIntent
            )
        }
    }

    fun cancelAlarm(alarmId: Long) {
        val intent = Intent(context, AlarmReceiver::class.java)
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            alarmId.toInt(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        alarmManager.cancel(pendingIntent)
    }

    private fun calculateNextAlarmTime(alarm: Alarm): Long {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        val today = now.date
        val currentTime = now.time

        return if (alarm.isOneTime) {
            // One-time alarm
            val alarmDateTime = if (alarm.time > currentTime) {
                // Today if time hasn't passed
                today.atTime(alarm.time)
            } else {
                // Tomorrow if time has passed
                today.plus(1, DateTimeUnit.DAY).atTime(alarm.time)
            }
            alarmDateTime.toInstant(TimeZone.currentSystemDefault()).toEpochMilliseconds()
        } else {
            // Recurring alarm
            calculateNextRecurringAlarmTime(alarm, today, currentTime)
        }
    }

    private fun calculateNextRecurringAlarmTime(
        alarm: Alarm,
        today: LocalDate,
        currentTime: LocalTime
    ): Long {
        if (alarm.repeatDays.isEmpty()) {
            // Daily alarm
            val alarmDateTime = if (alarm.time > currentTime) {
                today.atTime(alarm.time)
            } else {
                today.plus(1, DateTimeUnit.DAY).atTime(alarm.time)
            }
            return alarmDateTime.toInstant(TimeZone.currentSystemDefault()).toEpochMilliseconds()
        }

        // Weekly recurring alarm
        val todayDayOfWeek = today.dayOfWeek
        val sortedDays = alarm.repeatDays.sortedBy { it.ordinal }

        // Check if alarm should trigger today
        if (sortedDays.contains(todayDayOfWeek) && alarm.time > currentTime) {
            val alarmDateTime = today.atTime(alarm.time)
            return alarmDateTime.toInstant(TimeZone.currentSystemDefault()).toEpochMilliseconds()
        }

        // Find next day in the week
        val nextDay = sortedDays.firstOrNull { it.ordinal > todayDayOfWeek.ordinal }
            ?: sortedDays.first() // Wrap to next week

        val daysToAdd = if (nextDay.ordinal > todayDayOfWeek.ordinal) {
            nextDay.ordinal - todayDayOfWeek.ordinal
        } else {
            7 - todayDayOfWeek.ordinal + nextDay.ordinal
        }

        val nextAlarmDate = today.plus(daysToAdd, DateTimeUnit.DAY)
        val alarmDateTime = nextAlarmDate.atTime(alarm.time)
        return alarmDateTime.toInstant(TimeZone.currentSystemDefault()).toEpochMilliseconds()
    }

    companion object {
        const val EXTRA_ALARM_ID = "alarm_id"
        const val EXTRA_ALARM_LABEL = "alarm_label"
    }
}
