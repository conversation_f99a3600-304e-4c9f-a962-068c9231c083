package com.dailyplanner.alarm.speech

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.speech.RecognitionListener
import android.speech.RecognizerIntent
import android.speech.SpeechRecognizer
import android.util.Log
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.receiveAsFlow
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SpeechRecognitionManager @Inject constructor(
    private val context: Context
) {
    private var speechRecognizer: SpeechRecognizer? = null
    private val _recognitionResults = Channel<SpeechRecognitionResult>(Channel.BUFFERED)
    val recognitionResults: Flow<SpeechRecognitionResult> = _recognitionResults.receiveAsFlow()

    fun startListening() {
        if (!SpeechRecognizer.isRecognitionAvailable(context)) {
            _recognitionResults.trySend(
                SpeechRecognitionResult.Error("Speech recognition not available")
            )
            return
        }

        stopListening() // Stop any existing recognition

        speechRecognizer = SpeechRecognizer.createSpeechRecognizer(context).apply {
            setRecognitionListener(object : RecognitionListener {
                override fun onReadyForSpeech(params: Bundle?) {
                    Log.d("SpeechRecognition", "Ready for speech")
                    _recognitionResults.trySend(SpeechRecognitionResult.ReadyForSpeech)
                }

                override fun onBeginningOfSpeech() {
                    Log.d("SpeechRecognition", "Beginning of speech")
                    _recognitionResults.trySend(SpeechRecognitionResult.BeginningOfSpeech)
                }

                override fun onRmsChanged(rmsdB: Float) {
                    // Volume level changed - can be used for visual feedback
                    _recognitionResults.trySend(SpeechRecognitionResult.VolumeChanged(rmsdB))
                }

                override fun onBufferReceived(buffer: ByteArray?) {
                    // Audio buffer received
                }

                override fun onEndOfSpeech() {
                    Log.d("SpeechRecognition", "End of speech")
                    _recognitionResults.trySend(SpeechRecognitionResult.EndOfSpeech)
                }

                override fun onError(error: Int) {
                    val errorMessage = when (error) {
                        SpeechRecognizer.ERROR_AUDIO -> "Audio recording error"
                        SpeechRecognizer.ERROR_CLIENT -> "Client side error"
                        SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS -> "Insufficient permissions"
                        SpeechRecognizer.ERROR_NETWORK -> "Network error"
                        SpeechRecognizer.ERROR_NETWORK_TIMEOUT -> "Network timeout"
                        SpeechRecognizer.ERROR_NO_MATCH -> "No speech input"
                        SpeechRecognizer.ERROR_RECOGNIZER_BUSY -> "Recognition service busy"
                        SpeechRecognizer.ERROR_SERVER -> "Server error"
                        SpeechRecognizer.ERROR_SPEECH_TIMEOUT -> "No speech input"
                        else -> "Unknown error"
                    }
                    Log.e("SpeechRecognition", "Error: $errorMessage")
                    _recognitionResults.trySend(SpeechRecognitionResult.Error(errorMessage))
                }

                override fun onResults(results: Bundle?) {
                    val matches = results?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                    val confidence = results?.getFloatArray(SpeechRecognizer.CONFIDENCE_SCORES)
                    
                    if (!matches.isNullOrEmpty()) {
                        val bestMatch = matches[0]
                        val bestConfidence = confidence?.get(0) ?: 0f
                        Log.d("SpeechRecognition", "Result: $bestMatch (confidence: $bestConfidence)")
                        _recognitionResults.trySend(
                            SpeechRecognitionResult.Success(bestMatch, bestConfidence, matches)
                        )
                    } else {
                        _recognitionResults.trySend(
                            SpeechRecognitionResult.Error("No speech recognized")
                        )
                    }
                }

                override fun onPartialResults(partialResults: Bundle?) {
                    val matches = partialResults?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                    if (!matches.isNullOrEmpty()) {
                        _recognitionResults.trySend(
                            SpeechRecognitionResult.PartialResult(matches[0])
                        )
                    }
                }

                override fun onEvent(eventType: Int, params: Bundle?) {
                    // Additional events
                }
            })

            val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
                putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
                putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault())
                putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true)
                putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 3)
                putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS, 2000)
                putExtra(RecognizerIntent.EXTRA_SPEECH_INPUT_POSSIBLY_COMPLETE_SILENCE_LENGTH_MILLIS, 2000)
            }

            startListening(intent)
        }
    }

    fun stopListening() {
        speechRecognizer?.apply {
            stopListening()
            destroy()
        }
        speechRecognizer = null
    }

    fun isListening(): Boolean {
        return speechRecognizer != null
    }
}

sealed class SpeechRecognitionResult {
    object ReadyForSpeech : SpeechRecognitionResult()
    object BeginningOfSpeech : SpeechRecognitionResult()
    object EndOfSpeech : SpeechRecognitionResult()
    data class VolumeChanged(val volume: Float) : SpeechRecognitionResult()
    data class PartialResult(val text: String) : SpeechRecognitionResult()
    data class Success(
        val text: String,
        val confidence: Float,
        val allResults: List<String>
    ) : SpeechRecognitionResult()
    data class Error(val message: String) : SpeechRecognitionResult()
}
