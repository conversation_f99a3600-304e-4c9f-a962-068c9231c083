package com.dailyplanner.alarm.data.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "user_interactions")
data class UserInteraction(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val userInput: String,
    val inputType: InputType,
    val llmResponse: String,
    val timestamp: Long = System.currentTimeMillis(),
    val sessionId: String,
    val context: String? = null, // Additional context like current agenda items
    val audioFilePath: String? = null, // Path to recorded audio if applicable
    val responseAudioPath: String? = null // Path to generated response audio
)

enum class InputType {
    TEXT,
    VOICE,
    WAKE_UP_INTERACTION
}
