package com.dailyplanner.alarm.data.converters

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.datetime.DayOfWeek
import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalTime

class Converters {
    private val gson = Gson()

    @TypeConverter
    fun fromLocalTime(time: LocalTime?): String? {
        return time?.toString()
    }

    @TypeConverter
    fun toLocalTime(timeString: String?): LocalTime? {
        return timeString?.let { LocalTime.parse(it) }
    }

    @TypeConverter
    fun fromLocalDate(date: LocalDate?): String? {
        return date?.toString()
    }

    @TypeConverter
    fun toLocalDate(dateString: String?): LocalDate? {
        return dateString?.let { LocalDate.parse(it) }
    }

    @TypeConverter
    fun fromDayOfWeekSet(days: Set<DayOfWeek>?): String? {
        return days?.let { gson.toJson(it.map { day -> day.name }) }
    }

    @TypeConverter
    fun toDayOfWeekSet(daysJson: String?): Set<DayOfWeek>? {
        return daysJson?.let {
            val type = object : TypeToken<List<String>>() {}.type
            val dayNames: List<String> = gson.fromJson(it, type)
            dayNames.map { name -> DayOfWeek.valueOf(name) }.toSet()
        }
    }
}
