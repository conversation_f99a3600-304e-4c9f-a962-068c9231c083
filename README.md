# Daily Planner Alarm App

An intelligent Android alarm app that combines wake-up functionality with daily agenda management and AI-powered voice interaction.

## Features

### 🔔 Smart Alarm System
- Set multiple alarms with custom labels
- Recurring alarms with day-of-week selection
- One-time alarms for special occasions
- Snooze functionality with customizable duration
- Full-screen wake-up interface that works over lock screen

### 📅 Daily Agenda Management
- Add, edit, and organize daily tasks
- Priority levels (Low, Medium, High, Urgent)
- Time-based scheduling with duration estimates
- Task completion tracking
- Category organization

### 🤖 AI-Powered Assistant
- LLM integration for intelligent conversations
- Wake-up greetings with personalized agenda summaries
- Voice and text input support
- Motivational and productivity guidance
- Context-aware responses based on your schedule

### 🎤 Voice Interaction
- Speech-to-text for hands-free input
- Text-to-speech for AI responses
- Real-time voice recognition
- Audio recording and playback

## Architecture

### Technology Stack
- **Language**: Kotlin
- **UI Framework**: Jetpack Compose with Material 3
- **Architecture**: MVVM with Repository pattern
- **Database**: Room (SQLite)
- **Dependency Injection**: Hilt
- **Networking**: Retrofit + OkHttp
- **Async Processing**: Coroutines + Flow
- **Navigation**: Navigation Compose

### Key Components

#### Data Layer
- **Room Database**: Local storage for alarms, agenda items, and user interactions
- **Entities**: Alarm, AgendaItem, UserInteraction
- **DAOs**: Type-safe database access
- **Converters**: Handle complex data types (LocalTime, LocalDate, enums)

#### Domain Layer
- **AlarmScheduler**: Manages system alarm scheduling
- **LLMService**: Handles AI conversation logic
- **SpeechRecognitionManager**: Voice input processing
- **SpeechService**: Text-to-speech output

#### Presentation Layer
- **Compose UI**: Modern declarative UI
- **ViewModels**: State management and business logic
- **Navigation**: Bottom navigation with multiple screens

## Setup Instructions

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK 26+ (Android 8.0)
- Kotlin 1.9.10+

### Configuration

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd daily_planner_and_alarm
   ```

2. **Configure API Keys**
   - Open `app/src/main/java/com/dailyplanner/alarm/network/OpenAIApi.kt`
   - Replace `YOUR_API_KEY_HERE` with your OpenAI API key
   - Alternatively, add to your `local.properties`:
     ```
     OPENAI_API_KEY=your_api_key_here
     ```

3. **Build and Run**
   ```bash
   ./gradlew assembleDebug
   ```

### Permissions Required
The app requires several permissions for full functionality:
- `RECORD_AUDIO`: Voice input and commands
- `SCHEDULE_EXACT_ALARM`: Precise alarm timing
- `POST_NOTIFICATIONS`: Alarm and reminder notifications
- `WAKE_LOCK`: Keep device awake during alarms
- `SYSTEM_ALERT_WINDOW`: Show alarm over lock screen
- `VIBRATE`: Alarm vibration

## Usage

### Setting Up Alarms
1. Navigate to the "Alarms" tab
2. Tap the "+" button to create a new alarm
3. Set time, label, and repeat schedule
4. Enable/disable as needed

### Managing Your Agenda
1. Go to the "Agenda" tab
2. Add tasks with titles, descriptions, and priorities
3. Set time estimates and reminders
4. Mark items complete as you finish them

### Interacting with AI Assistant
1. Use the "Assistant" tab for general conversations
2. Voice commands work throughout the app
3. The AI provides wake-up summaries when alarms trigger
4. Ask for help with planning and organizing your day

### Wake-Up Experience
When an alarm triggers:
1. Full-screen interface appears over lock screen
2. AI greets you with a personalized message
3. Today's agenda is displayed with priorities
4. Choose to stop or snooze the alarm
5. Interact with the AI for planning adjustments

## Development

### Project Structure
```
app/src/main/java/com/dailyplanner/alarm/
├── data/
│   ├── entities/          # Room entities
│   ├── dao/              # Database access objects
│   ├── database/         # Database configuration
│   └── converters/       # Type converters
├── di/                   # Dependency injection modules
├── alarm/               # Alarm system components
├── speech/              # Speech processing
├── llm/                 # AI integration
├── network/             # API interfaces
└── ui/                  # User interface
    ├── screens/         # Screen composables
    ├── components/      # Reusable UI components
    ├── navigation/      # Navigation setup
    └── theme/           # Material 3 theming
```

### Key Design Decisions
- **Room Database**: Chosen for robust local storage with type safety
- **Jetpack Compose**: Modern UI toolkit for better developer experience
- **Hilt**: Simplified dependency injection
- **Material 3**: Latest design system for consistent UX
- **Coroutines**: Structured concurrency for async operations

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- OpenAI for LLM capabilities
- Google for Android development tools
- Material Design team for UI guidelines
