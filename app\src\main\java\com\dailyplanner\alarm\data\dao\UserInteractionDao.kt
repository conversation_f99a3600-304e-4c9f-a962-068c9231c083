package com.dailyplanner.alarm.data.dao

import androidx.room.*
import com.dailyplanner.alarm.data.entities.UserInteraction
import com.dailyplanner.alarm.data.entities.InputType
import kotlinx.coroutines.flow.Flow

@Dao
interface UserInteractionDao {
    
    @Query("SELECT * FROM user_interactions ORDER BY timestamp DESC")
    fun getAllInteractions(): Flow<List<UserInteraction>>
    
    @Query("SELECT * FROM user_interactions WHERE sessionId = :sessionId ORDER BY timestamp ASC")
    fun getInteractionsBySession(sessionId: String): Flow<List<UserInteraction>>
    
    @Query("SELECT * FROM user_interactions WHERE inputType = :inputType ORDER BY timestamp DESC")
    fun getInteractionsByType(inputType: InputType): Flow<List<UserInteraction>>
    
    @Query("SELECT * FROM user_interactions ORDER BY timestamp DESC LIMIT :limit")
    fun getRecentInteractions(limit: Int): Flow<List<UserInteraction>>
    
    @Insert
    suspend fun insertInteraction(interaction: UserInteraction): Long
    
    @Update
    suspend fun updateInteraction(interaction: UserInteraction)
    
    @Delete
    suspend fun deleteInteraction(interaction: UserInteraction)
    
    @Query("DELETE FROM user_interactions WHERE timestamp < :cutoffTime")
    suspend fun deleteOldInteractions(cutoffTime: Long)
    
    @Query("SELECT COUNT(*) FROM user_interactions WHERE sessionId = :sessionId")
    suspend fun getInteractionCountForSession(sessionId: String): Int
}
