package com.dailyplanner.alarm.di

import android.content.Context
import androidx.room.Room
import com.dailyplanner.alarm.data.database.DailyPlannerDatabase
import com.dailyplanner.alarm.data.dao.AlarmDao
import com.dailyplanner.alarm.data.dao.AgendaDao
import com.dailyplanner.alarm.data.dao.UserInteractionDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideDatabase(@ApplicationContext context: Context): DailyPlannerDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            DailyPlannerDatabase::class.java,
            DailyPlannerDatabase.DATABASE_NAME
        )
        .fallbackToDestructiveMigration()
        .build()
    }

    @Provides
    fun provideAlarmDao(database: DailyPlannerDatabase): AlarmDao {
        return database.alarmDao()
    }

    @Provides
    fun provideAgendaDao(database: DailyPlannerDatabase): AgendaDao {
        return database.agendaDao()
    }

    @Provides
    fun provideUserInteractionDao(database: DailyPlannerDatabase): UserInteractionDao {
        return database.userInteractionDao()
    }
}
